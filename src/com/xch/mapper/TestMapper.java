package com.xch.mapper;

import com.xch.entity.User;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.scripting.xmltags.XMLLanguageDriver;

import java.util.List;

public interface TestMapper {

    @Select("select * from user")
    List<User> selectAllUser();

    @Lang(XMLLanguageDriver.class)
    @Select("""
        <script>
                    select * from user where id = #{id}
                    <if test="id > 3">
                         and age > 18
                    </if>
                 </script>
""")
    User selectUserById(User user);


    int insertUser(User user);

    int updateAgeById(User user);
}
