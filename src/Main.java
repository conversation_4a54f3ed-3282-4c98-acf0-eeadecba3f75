import com.xch.entity.Idea;

import java.util.Arrays;

public class Main {
    public static void main(String[] args) throws Exception {
        int[] nums = {2,3,4,6,5};
        moveZeroes(nums);
        System.out.println(Arrays.toString(nums));

        Idea i1 = Idea.build("");
        System.out.println(i1.getIdea());
    }
    //
    public static void moveZeroes(int[] nums) {
        int len = nums.length;
        int slow = 0, fast = 0;
        while(fast < len){
            if(nums[fast] != 0){
                nums[slow++] = nums[fast++];
            } else{
                fast++;
            }
        }
        while(slow < len){
            nums[slow++] = 0;
        }
    }
}
