<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="logImpl" value="STDOUT_LOGGING"/> <!-- 配置日志 -->
    </settings>
    <typeAliases>
        <package name="com.xch.entity"/>
    </typeAliases>
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.cj.jdbc.Driver"/>
                <property name="url" value="*******************************************"/>
                <property name="username" value="shaunhighmore"/>
                <property name="password" value="lBMdf65xDzAJNsS8"/>
            </dataSource>
        </environment>
    </environments>
    <mappers>
        <package name="com.xch.mapper"/>
    </mappers>
</configuration>